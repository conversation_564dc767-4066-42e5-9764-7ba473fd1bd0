import argparse
import ctypes
import os
import sys
import time
import threading
from scanner import StatsScanner


class bcolors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


DESC = (
    "This program reads the stats of the top N players in the individual power ranking list. It controls RoK PC "
    "Version and parses them using the Tesseract OCR. "
)

parser = argparse.ArgumentParser(description=DESC)
parser.add_argument(
    "-t",
    "--tag",
    help="The name of the task (for example, KvK-Start, KvK-pass4). It will be used as the name of the output folder.",
    required=True,
)
parser.add_argument(
    "-n",
    "--number",
    help="How many profiles to read from the top of the list",
    required=True,
    type=int,
)

arguments = parser.parse_args()


def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


if __name__ == "__main__":

    # Check if tesseract is installed
    if not StatsScanner.is_tool("tesseract"):
        print(f"{bcolors.FAIL}We could not find the required Tesseract OCR executable in the PATH "
              f"variable.\nPlease refer to the documentation to install Tesseract, add it to the PATH, and restart "
              f"IN A NEW TERMINAL.{bcolors.ENDC}")
        sys.exit()

    params = {
        "device": "pc",
        "window": "Rise of Kingdoms",
        "count": arguments.number,
        "dir": os.getcwd() + "\\output",
        "tag": arguments.tag
    }

    # The program must be running in administration mode. If not restart with admin privileges.
    if not is_admin():
        print(f"{bcolors.FAIL}To run with the PC version, this application must run as administrator.\nPlease "
              f"Start a new terminal AS an ADMINISTRATOR.{bcolors.ENDC}")
        sys.exit()

    print(f"{bcolors.OKGREEN}Required resolution: {bcolors.OKCYAN}Full Screen 1920x1080")
    print(
        f"{bcolors.OKGREEN}RoK must be running at the {bcolors.OKCYAN}individual power ranking{bcolors.OKGREEN} page. Switching to the game in 5 seconds ...{bcolors.ENDC}")
    time.sleep(6)

    stopEvent = threading.Event()
    scanner = StatsScanner(params, stopEvent)
    scanner.start()
